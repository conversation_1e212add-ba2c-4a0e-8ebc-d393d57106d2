### 本质复杂度和偶然复杂度

本质复杂度就是解决一个问题时，无论怎么做都必须要做的事，而偶然复杂度是因为选用的做事方法不当，而导致要多做的事。

### 工作方法

- 以终为始：就是在工作的一开始就确定好自己的目标。我们需要看到的是真正的目标，而不是把别人交代给我们的工作当作目标，这就需要我们多沟通反馈，明确用户真正的目标到底是什么，不要自己脑补。
- 任务分解：将大目标拆分成一个一个可行的执行任务，工作分解得越细致，我们便越能更好地掌控工作。
- 沟通反馈：疏通与其他人交互的渠道。一方面，我们保证信息能够传达出去，减少因为理解偏差造成的工作疏漏；另一方面，也要保证我们能够准确接收外部信息，以免因为自我感觉良好，阻碍了进步。
- 自动化：将繁琐的工作通过自动化的方式交给机器执行，这是我们程序员本职工作的一部分，我们擅长的是为其他人打造自动化的服务，但自己的工作却应用得不够，这也是我们工作中最值得优化的部分。

反思一下自己最近做的压测置信度治理上，首先以终为始，在业务指标的治理开始时明确了治理流程（内外圈治理）和需要治理的业务/性能指标，明确调度的压测置信度最终要达到一个怎样的状态。任务分解的粒度比较粗，整个任务分解成输入指标治理、输出指标治理。其实还可以在细致一点，比如：梳理指标，增加打点，配置大盘等。沟通反馈上每周五会有周会，大家及时沟通反馈进度和问题，其实在沟通反馈上不一定要等到周五一起反馈，遇到问题后可以第一时间反馈和寻找方案，这样效率会高一些。最后自动化这一块虽然也做了很多，但是很多工作还是需要人工去处理，比如数据的拉取，分析，样本采集等，自动化这一块还可以进一步完善。

### 收藏语句

 - 知识有两种类型，第一类知识注重了解某个事物的名称，第二类知识注重了解某件事物，这可不是一回事儿，我们绝大多数人关注的都是错误的第一类。
 - 通过物理隔绝远离诱惑，要比靠意志力抗拒诱惑容易太多了。
 - 你对我的百般注解也构成不了我的万分之一，却是一览无余的你自己。




