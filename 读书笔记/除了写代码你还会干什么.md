

## 除了写代码你还会干什么

​	今天看到一篇公众号的文章，原题目叫做《程序员压力真的这么大吗？》，文档以最近发生的42 岁中兴网信研发负责人欧某被辞退后坠亡的新闻作为引子，引出程序员的中年危机，中年的程序员背负着4座大山：房贷、车贷、老人赡养、子女教育。最怕的就是一是钱不够，二是离职。文章对于中年危机的原因提到了3点：（1）身体素质越来越差，精力跟不上；（2）沟通能力大都不太好；（3）专注技术，不做技术的时候就不知道干什么了。

​	这三点原因可是是引发中年危机最重要的三点，由此也引发了自己思考，既然知道这是程序员的通病，那么自己该如何避免呢？

1. 对于身体素质差，大多程序员都比较宅。刚入职的一段时间，一到周末（虽然很多时候只有一天）就是看电影，打游戏，颓废一天，各种愧疚，头脑昏胀的感觉涌了上来，贼难受，可不打游戏不看电影又能做些什么呢。后来越来越意识到这个情况的严重性，我尝试着”走出去“，给自己定了一个目标，每周末都要到南京的一个地方，争取半年把南京转完。我先后去了玄武湖、南京图书馆、1912酒吧一条街、艺术工坊、南京大学、大众书局这些地方，经过一段时间的”走出去“，周末我不再宅在家里，后来图书馆和大众书局就成了经常光顾的地方，去看看书，学习一下Java，充充电，感觉一天过得很充实，很欣慰。

   接下来的目标就是锻炼身体！

2. 对于沟通能力。这一点我自认为还可以，毕竟在菊厂也和各种人打过交道。唯一觉得自己不足的就是没有正事的时候，对于评论和看法我不是很擅长表达，经常都是作为一个旁听者的身份。

3. 对于专注技术。这个是我感触最深的地方，也是促使我写这边观后感的原因，文章中讲了一个故事，一个北京的程序员。2016年，在父母和亲戚的游说下，把北京90多平的三房卖了500万，回到18线小城买了两套房，借了一些亲戚，手头还剩300万，于是买理财产品，吃利息过生活。一段时间后，想找点工作干，找来找去，唯一与电脑相关的就是网管。干了几个月的网管工作，每个月才拿两千左右，他觉得很空虚，很焦虑。于是，拿着剩下的300万回到了北京，当时卖掉的那套房子已经涨到850万。他在同一个小区买了一个小两房，继续开始当程序员，他说这样的日子让自己踏实、开心。

   我觉得年轻的时候专注于技术肯定是件好事，技术大牛真的很吃香。但随着年龄的渐渐增长，自己也要考虑一下培养自己的”副产品“，比如读书，英语，演讲，创意...这些跟写代码并没有冲突，但是需要自己主动去培养这方面的能力。我一直很羡慕那些技术很牛而且很能写或者很能说的人才，自己也在像哪方面靠拢，毕竟只会写代码的程序员太”无聊“了。毕竟自己心中还有一个创业梦。

   ​

> 读文章有感：https://mp.weixin.qq.com/s/Hy8BoMPOPFzFsKxHKMIe7Q
>
> 2018年02月23日20:25:39 南京