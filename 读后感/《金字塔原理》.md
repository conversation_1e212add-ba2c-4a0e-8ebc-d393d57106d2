### 《金字塔原理》

没有什么比一套好的理论更有用了。

#### 一、简介

《金字塔原理》介绍了一种清晰的展现思路的有效方法。对培养逻辑和结构化思考很有帮助。

#### 二、笔记

---

**第一章：表达的逻辑**


金字塔原理的4个原则：
1. 结论先行：每篇文章只有一个中心思想，且应该放在文章的最前面；
2. 以上统下：每一层次的思想必须是对下一层次思想的总结概括；
3. 归类分组：每一组中的思想必须属于同一逻辑范畴；
4. 逻辑递进：每一组中的思想必须按照逻辑顺序排列。


自上而下表达，自下而上思考总结。

一篇好文章的思路应该符合以下规则：
 - 文章中任意层次的思想必须是下一层次思想的概括；
 - 每组中的思想必须属于同一逻辑范畴；（第6,7章阐述如何确保将相同范畴的思想组织在一起）
 - 每组中的思想必须按照逻辑顺序[^1]组织。（第6章阐述如何合理确定逻辑顺序）

调整你的思路，使其符合金字塔结构，如何构建金字塔：自上而下或者自下而上。
1. 自上而下法：定主题、想疑问、给答案、做检查、证答案、写要点；
2. 自下而上法：列要点、找关系、得结论、推序言。


##### 2.1 如何写好序言

一个好的序言需要包含4个要素SCQA：
1. S：Situation，介绍背景；
2. C：Complication，指出冲突；
3. Q：Question，引发疑问；
4. A：Answer，给出答案。

##### 2.2 演绎推理和归纳推理

 - **演绎推理：** 演绎是一种线性的推理方式，是由一般到特殊，比如人会死，苏格拉底是人，所以苏格拉底会死。再举个例子，做好这4项工作就能提升公司收益，但贵公司目前的组织结构不可能做好4项中的任何一项，因此贵公司应该改变现有结构；
 - **归纳推理：** 归纳是由一组具有共同点的事实、思想或观点，概括其共性，是由部分到整体，比如我有两条腿，你有两条腿，他也有两条腿，你我他都是人，所以人有两条腿。

在实际的写作中，演绎推理会非常繁琐，因此我建议在关键句层次尽量避免使用演绎推理，而使用归纳推理，归纳法更便于读者阅读和理解。下面举一个例子来说明为什么演绎法理解起来比较困难。
一家公司经过调研人员研究后得出必须进行改革的结论，接下来分别用演绎法和归纳法向老板说明必须进行改革的报告：

**演绎法**

![](assets/演绎推理案例.png)

按照演绎推理的逻辑，读者必须先理解和接受目前存在的问题，在带着问题理解和接受产生问题的原因，对对应起来(问题1对应原因1，问题2对应原因2...)，然后读者在将对应的举措联系起来。这种方式读者需要费尽周章才能知道要采取什么措施。

**归纳法**

![](assets/归纳推理案例.png)

可以看到归纳法只是将述职顺序换了一下，先提出如何进行，再提出为什么。读者很清晰的快速就知道了需要采取什么措施（读者的主要疑问）。然后再关注为什么的次要疑问。

**什么时候用演绎法，什么时候会归纳法**

读者的重点在于**为什么**的时候用演绎法，先说明问题和原因；读者的重点在于**怎么做**的时候用归纳法，快速提出方案再阐述原因。

---

**第二章：思考的逻辑**

写作应该避免：
1. 用关联性很弱的思想列在一起，比如10个问题等，这类标题不具有启发性，思想性；
2. 在写文章时检查每组思想是否存在逻辑顺序[^1]，如果不存在逻辑顺序则说明你的分组有问题，你就应该应用逻辑分析框架找出问题所在；

---

**第三章：解决问题的逻辑**

结构化分析问题的步骤：收集问题->描述发现->得出结论->提出方案。很多情况下，全面收集问题再去分析很劳民伤财，一个行之有效的方法是在收集资料之前对问题进行结构化分析。换句话说就是强迫自己思考产生问题的各种原因，这种技巧叫做外展推理。

---

**第四章：演示的逻辑**

选择什么样的演示形式，取决于内容的长短和受众人数。如果内容比较短并且受众人数很少，就可以简化的用文本的形式，几个词或者几句话就说清楚，尤其是一个大项目中需要确认的某个小点，没必要把整个背景都描述一遍，只选择和待讨论问题相关的背景说清楚就行。

无论采用哪种演示形式，在视觉上都要确保能加强金字塔思想之间的逻辑性和关联性。

理想的文章应该能让读者在30秒内理解作者的整体思维架构。

写篇幅较长的文章时，在页面上呈现金字塔层级的方法很多：
1. 多级标题法；
2. 下划线法；（突出要点）
3. 数字编号法；
4. 行首缩进法；
5. 项目符号法。

从一组观点转到另一组观点时，一定要有过渡句，以免让读者感觉太突兀。

每组标题应提前介绍：不要紧接着大标题下面写小标题，而是先总结一下这部门主要内容。否则读者读完整部分才能理解你大标题的意思。

文章最后加上结论（原则：写不好不如不写），阐明你表达内容的重要性。比如下面是一篇报告的总结段落，报告要传达的信息是，建立一个遍及全欧洲的、可以快速检索技术文献的计算机查询系统，在技术上是可行的：
> 如果能成功推出该系统，不仅可以使工商业、相应行业或学术界的用户更快地获取科技资讯，而且可以创造一个广阔的信息市场，向所有用户，而不只是国家机构，提供现有全部资源。这不仅可以引领标准化和一体化的发展，还可能发展出全新的标准。我们认为前景是激动人心的，也非常期待和您一起推出试验计划。

PPT演示技巧：
1. 图文的理想比例是：图片90%，文字10%；
2. 观众关注的重点是你，PPT只是辅助手段，PPT只放最重要的总结，因此你演讲的内容是对PPT的解释和扩充，禁忌你演讲的内容和PPT内容完全一致；
3. 每张幻灯片最好不要超过6行或超过30个单词；
4. 


---

#### 三、一些名词收集

 - 非期望结果：由于一些背景问题导致出现一些非期望结果，就是现状；
 - 期望结果：由于存在一些问题，我们想通过一些手段解决并达成我们的期望结果，就是目标；
 - 结构化分析问题
 - 诊断框架
 - 逻辑树
 - 界定问题
 - 外展推理


#### 四、附录三「本书要点」很精辟，后面可以多参考！





[^1]: 时间(步骤)顺序、结构(空间)顺序、程度(重要性)顺序；


