

# Obsidian 社区插件下载修复

因为某些原因，在国内经常无法下载 Obsidian 的社区插件。这个项目的主要目的就是修复这种情况，让国内的用户也可以无障碍的下载社区插件。

<!-- PROJECT SHIELDS -->

[![Contributors][contributors-shield]][contributors-url]
[![Forks][forks-shield]][forks-url]
[![Stargazers][stars-shield]][stars-url]
[![Issues][issues-shield]][issues-url]
[![MIT License][license-shield]][license-url]
[![LinkedIn][linkedin-shield]][linkedin-url]

<!-- PROJECT LOGO -->
<br />



## 目录

- [上手指南](#上手指南)
- [文件目录说明](#文件目录说明)
- [作者](#作者)
- [版权说明](#版权说明)
- [鸣谢](#鸣谢)

### 上手指南

1. 下载 [obsidian-proxy-github.zip](https://gitee.com/juqkai/obsidian-proxy-github/releases)
2. 解压 obsidian-proxy-github.zip
3. 将解压的文件夹放入笔记目录下的插件目录内。如：XXX/.obsidian/plugins


### 作者

<EMAIL>

### 版权说明

该项目签署了 Apache License 2.0 授权许可，详情请参阅 [LICENSE.txt](https://github.com/juqkai/obsidian-proxy-github/blob/master/LICENSE)

### 鸣谢


- [Obsidian](https://Obsidian.md)

