/*----------------------------------------------------------------
CMENU TOOLBAR
----------------------------------------------------------------*/

#cMenuModalBar {
  width: auto;
  height: auto;
  padding: 3px;
  display: grid;
  user-select: none;
  border-radius: 6px;
  position: absolute;
  transition: 200ms ease;
  min-width: fit-content;
  justify-content: space-around;
  z-index: var(--layer-status-bar);
  box-shadow: 0px 3px 32px rgb(31 38 135 / 15%);
  border: 1px solid var(--background-modifier-border);
}

#cMenuModalBar .cMenuCommandItem {
  margin: 2px;
  border: none;
  display: flex;
  cursor: pointer;
  padding: 5px 6px;
  box-shadow: none;
  margin-left: 3px;
  margin-right: 3px;
  position: relative;
  border-radius: 3px;
  font-size: initial !important;
  background-color: var(--background-primary-alt);
}

#cMenuModalBar button.cMenuCommandItem:hover {
  background-color: var(--background-secondary);
}

#cMenuModalBar button.cMenuCommandItem svg {
  width: 1.3em;
  height: 1.3em;
}

/*----------------------------------------------------------------
CMENU SETTINGS BUTTONS
----------------------------------------------------------------*/

.modal.mod-settings
  button:not(.mod-cta):not(.mod-warning).cMenuSettingsButton.cMenuSettingsButtonAdd,
button:not(.mod-cta):not(.mod-warning).cMenuSettingsButton.cMenuSettingsButtonAdd {
  background-color: var(--interactive-accent);
}

.modal.mod-settings
  button:not(.mod-cta):not(.mod-warning).cMenuSettingsButton.cMenuSettingsButtonDelete,
button:not(.mod-cta):not(.mod-warning).cMenuSettingsButton.cMenuSettingsButtonDelete {
  background-color: #989cab;
}

.modal.mod-settings
  button:not(.mod-cta):not(.mod-warning).cMenuSettingsButton.cMenuSettingsButtonRefresh,
button:not(.mod-cta):not(.mod-warning).cMenuSettingsButton.cMenuSettingsButtonRefresh {
  background-color: var(--text-accent);
}

button.cMenuSettingsButton {
  padding: 4px 14px;
  border-radius: 6px;
}

.cMenuSettingsButton svg {
  width: 1.3em;
  height: 1.3em;
  display: flex;
}

/*----------------------------------------------------------------
CMENU SETTING ITEMS
----------------------------------------------------------------*/
.setting-item.cMenuCommandItem:first-child {
  padding-top: 18px;
}

.cMenuCommandItem {
  cursor: grab;
  padding: 18px 0 18px 0;
}

.sortable-fallback {
  cursor: grabbing;
  box-shadow: 0px 3px 32px rgb(31 38 135 / 15%);
}

.sortable-grab {
  cursor: grabbing !important;
}

.sortable-ghost {
  opacity: 0.4;
  cursor: grabbing;
}

.sortable-chosen {
  cursor: grabbing;
  padding: 18px 0 18px 18px;
  background-color: var(--background-primary);
}

.sortable-drag {
  cursor: grabbing;
  box-shadow: 0px 3px 32px rgb(31 38 135 / 15%);
}

.cMenuSettingsTabsContainer {
  border-top: 1px solid var(--background-modifier-border);
  border-bottom: 1px solid var(--background-modifier-border);
}

/*----------------------------------------------------------------
CMENU CLASS CHANGES
----------------------------------------------------------------*/

.cMenuGlassAesthetic {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.cMenuDefaultAesthetic {
  background-color: var(--background-primary);
}

/*----------------------------------------------------------------
CMENU ICONS
----------------------------------------------------------------*/

.cMenuIconPick {
  line-height: normal;
  vertical-align: middle;
  margin-right: 8px;
}

.cMenuIconPick svg {
  width: 17px;
  height: 17px;
}

/*----------------------------------------------------------------
CMENU STATUS BAR MENU
----------------------------------------------------------------*/

.cMenu-statusbar-menu {
  width: 200px;
}

.cMenu-statusbar-menu .menu-item-icon {
  display: none;
}

.cMenu-statusbar-menu .menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row;
}

.cMenu-statusbar-menu .menu-item.settings-item {
  font-size: 12px;
  text-align: center;
  line-height: 1;
  border-radius: 5px;
  height: auto;
  padding: 8px 5px 0px 5px;
  margin: 0 auto;
  width: fit-content;
  color: var(--text-faint);
}

.cMenu-statusbar-menu .menu-item.settings-item .menu-item-title {
  text-align: center;
}

.cMenu-statusbar-menu .menu-item:hover,
.cMenu-statusbar-menu .menu-item .selected:hover,
.cMenu-statusbar-menu .menu-item.selected:not(.is-disabled):not(.is-label) {
  background-color: transparent;
}

.cMenu-statusbar-menu .menu-item-title {
  margin-right: 10px;
}

.cMenu-statusbar-menu .slider {
  width: 100%;
}

.cMenu-statusbar-menu .menu-item.buttonitem {
  padding-top: 10px;
  padding-bottom: 4px;
  height: fit-content;
}

.cMenu-statusbar-menu .menu-item.buttonitem button.cMenuSettingsButton {
  margin: 0;
}

/*----------------------------------------------------------------
CMENU STATUS BAR BUTTONS
----------------------------------------------------------------*/

.cMenu-statusbar-button {
  cursor: pointer;
  display: flex;
  align-items: center;
  line-height: 1;
}

.cMenu-statusbar-button svg {
  display: flex;
  width: 1.3em;
  height: 1.3em;
}

/*----------------------------------------------------------------
CMENU SUPPORT
----------------------------------------------------------------*/

.cDonationSection {
  width: 60%;
  height: 50vh;
  margin: 0 auto;
  text-align: center;
  color: var(--text-normal);
}
